================================================================================
                    NANOSTAT FLASK REST API 完整实现文档
================================================================================

版本: 1.0
更新时间: 2024-01-15
技术栈: Flask + MongoDB + PyMongo + Marshmallow + Flask-CORS

================================================================================
                              目录
================================================================================
1. 项目结构
2. 技术栈和依赖
3. 数据模型实现
4. API路由实现
5. 数据验证模式
6. 工具函数
7. 配置管理
8. 主应用文件
9. 环境配置
10. 测试实现
11. 部署说明

================================================================================
                          1. 项目结构
================================================================================

nanostat-flask-api/
├── app.py                      # 主应用文件
├── config.py                   # 配置管理
├── models.py                   # 数据模型
├── schemas.py                  # 数据验证模式
├── routes.py                   # API路由
├── utils.py                    # 工具函数
├── requirements.txt            # 依赖包
├── .env                        # 环境变量
├── logs/                       # 日志目录
│   └── app.log
└── tests/                      # 测试文件
    └── test_api.py

================================================================================
                          2. 技术栈和依赖
================================================================================

核心框架:
- Flask==2.3.3                 # Web框架
- Flask-RESTful==0.3.10        # REST API扩展
- Flask-CORS==4.0.0            # 跨域支持

数据库:
- pymongo==4.5.0               # MongoDB驱动

数据验证:
- marshmallow==3.20.1          # 数据序列化和验证

配置管理:
- python-dotenv==1.0.0         # 环境变量管理

================================================================================
                          3. 数据模型实现
================================================================================

3.1 数据库连接类 (models.py)
----------------------------
from pymongo import MongoClient
from datetime import datetime
import os

class Database:
    def __init__(self):
        mongo_uri = os.getenv('MONGODB_URI', 'mongodb://localhost:27017/')
        self.client = MongoClient(mongo_uri)
        self.db = self.client.nanostat
        
        # 集合引用
        self.glucose_readings = self.db.glucose_readings
        self.devices = self.db.devices
        
        # 创建索引
        self._create_indexes()
    
    def _create_indexes(self):
        """创建数据库索引以提高查询性能"""
        # glucose_readings 索引
        self.glucose_readings.create_index([("device_id", 1), ("timestamp", -1)])
        self.glucose_readings.create_index("received_at")
        self.glucose_readings.create_index("device_id")
        
        # devices 索引
        self.devices.create_index("device_id", unique=True)

3.2 葡萄糖读数模型
------------------
class GlucoseReading:
    def __init__(self, db):
        self.collection = db.glucose_readings
    
    def create(self, data):
        """创建新的葡萄糖读数记录"""
        reading = {
            'device_id': data['id'],
            'timestamp': datetime.fromisoformat(data['timestate'].replace('Z', '+00:00')),
            'timestate': data['timestate'],
            'current': float(data['current']),
            'voltage': float(data['voltage']),
            'metadata': data.get('metadata', {}),
            'received_at': datetime.utcnow(),
            'processed': False,
            'quality_score': self._calculate_quality_score(data)
        }
        
        result = self.collection.insert_one(reading)
        reading['_id'] = str(result.inserted_id)
        return reading
    
    def find_by_device(self, device_id, limit=1000, skip=0, start_date=None, end_date=None):
        """根据设备ID查询读数"""
        query = {'device_id': device_id}
        
        if start_date or end_date:
            query['timestamp'] = {}
            if start_date:
                query['timestamp']['$gte'] = start_date
            if end_date:
                query['timestamp']['$lte'] = end_date
        
        cursor = self.collection.find(query).sort('timestamp', -1).skip(skip).limit(limit)
        readings = []
        for doc in cursor:
            doc['_id'] = str(doc['_id'])
            readings.append(doc)
        return readings
    
    def find_latest(self, device_id):
        """获取设备最新读数"""
        doc = self.collection.find_one(
            {'device_id': device_id},
            sort=[('timestamp', -1)]
        )
        if doc:
            doc['_id'] = str(doc['_id'])
        return doc
    
    def get_statistics(self, device_id, start_date=None, end_date=None):
        """获取设备统计数据"""
        match_stage = {'device_id': device_id}
        if start_date or end_date:
            match_stage['timestamp'] = {}
            if start_date:
                match_stage['timestamp']['$gte'] = start_date
            if end_date:
                match_stage['timestamp']['$lte'] = end_date
        
        pipeline = [
            {'$match': match_stage},
            {'$group': {
                '_id': None,
                'total_readings': {'$sum': 1},
                'avg_current': {'$avg': '$current'},
                'avg_voltage': {'$avg': '$voltage'},
                'min_current': {'$min': '$current'},
                'max_current': {'$max': '$current'},
                'min_voltage': {'$min': '$voltage'},
                'max_voltage': {'$max': '$voltage'}
            }}
        ]
        
        result = list(self.collection.aggregate(pipeline))
        return result[0] if result else {}
    
    def _calculate_quality_score(self, data):
        """计算数据质量分数"""
        score = 1.0
        
        # 检查数值范围
        if abs(data['current']) > 500:
            score -= 0.2
        if abs(data['voltage']) > 1500:
            score -= 0.2
        
        return max(0.0, score)

3.3 设备模型
------------
class Device:
    def __init__(self, db):
        self.collection = db.devices
    
    def create_or_update(self, device_id, name=None, location=None):
        """创建或更新设备信息"""
        device_data = {
            'device_id': device_id,
            'last_seen': datetime.utcnow(),
            'status': 'online',
            'updated_at': datetime.utcnow()
        }
        
        if name:
            device_data['name'] = name
        if location:
            device_data['location'] = location
        
        result = self.collection.update_one(
            {'device_id': device_id},
            {
                '$set': device_data,
                '$setOnInsert': {
                    'created_at': datetime.utcnow(),
                    'name': name or device_id,
                    'location': location or ''
                }
            },
            upsert=True
        )
        
        return self.find_by_id(device_id)
    
    def find_by_id(self, device_id):
        """根据设备ID查找设备"""
        doc = self.collection.find_one({'device_id': device_id})
        if doc:
            doc['_id'] = str(doc['_id'])
        return doc
    
    def find_all(self):
        """获取所有设备"""
        devices = []
        for doc in self.collection.find():
            doc['_id'] = str(doc['_id'])
            devices.append(doc)
        return devices

================================================================================
                          4. 数据验证模式 (schemas.py)
================================================================================

from marshmallow import Schema, fields, validate, validates, ValidationError
from datetime import datetime
import re

class GlucoseReadingSchema(Schema):
    """葡萄糖读数数据验证模式"""
    id = fields.Str(
        required=True, 
        validate=validate.Length(max=50),
        error_messages={'required': 'Device ID is required'}
    )
    timestate = fields.Str(
        required=True,
        error_messages={'required': 'Timestamp is required'}
    )
    current = fields.Float(
        required=True,
        validate=validate.Range(min=-1000, max=1000),
        error_messages={
            'required': 'Current value is required',
            'invalid': 'Current must be a number between -1000 and 1000'
        }
    )
    voltage = fields.Float(
        required=True,
        validate=validate.Range(min=-2000, max=2000),
        error_messages={
            'required': 'Voltage value is required',
            'invalid': 'Voltage must be a number between -2000 and 2000'
        }
    )
    metadata = fields.Dict(missing={})
    
    @validates('id')
    def validate_device_id(self, value):
        """验证设备ID格式"""
        if not re.match(r'^NANOSTAT_\d{3}$', value):
            raise ValidationError('Device ID must follow pattern NANOSTAT_XXX')
    
    @validates('timestate')
    def validate_timestate(self, value):
        """验证时间戳格式"""
        try:
            datetime.fromisoformat(value.replace('Z', '+00:00'))
        except ValueError:
            raise ValidationError('Invalid ISO format timestamp')

class DeviceSchema(Schema):
    """设备信息验证模式"""
    device_id = fields.Str(required=True, validate=validate.Length(max=50))
    name = fields.Str(validate=validate.Length(max=100))
    location = fields.Str(validate=validate.Length(max=200))

class QuerySchema(Schema):
    """查询参数验证模式"""
    start = fields.DateTime(missing=None, format='iso')
    end = fields.DateTime(missing=None, format='iso')
    limit = fields.Int(validate=validate.Range(min=1, max=10000), missing=1000)
    offset = fields.Int(validate=validate.Range(min=0), missing=0)
    format = fields.Str(validate=validate.OneOf(['json', 'csv']), missing='json')

================================================================================
                          5. API路由实现 (routes.py)
================================================================================

from flask import Blueprint, request, jsonify, make_response
from marshmallow import ValidationError
from schemas import GlucoseReadingSchema, DeviceSchema, QuerySchema
from models import GlucoseReading, Device
from utils import create_response, export_to_csv, setup_logger
from datetime import datetime, timedelta

# 创建蓝图
api_bp = Blueprint('api', __name__, url_prefix='/api')
logger = setup_logger(__name__)

# 全局变量（在app.py中初始化）
glucose_model = None
device_model = None

def init_routes(db):
    """初始化路由，设置数据库模型"""
    global glucose_model, device_model
    glucose_model = GlucoseReading(db)
    device_model = Device(db)

5.1 接收葡萄糖数据
------------------
@api_bp.route('/glucose', methods=['POST'])
def receive_glucose_data():
    """接收NanoStat设备的葡萄糖数据"""
    try:
        # 验证请求数据
        schema = GlucoseReadingSchema()
        data = schema.load(request.json)
        
        # 创建读数记录
        reading = glucose_model.create(data)
        
        # 更新设备状态
        device_model.create_or_update(data['id'])
        
        # 记录日志
        logger.info(f"Data received from device {data['id']}: "
                   f"current={data['current']}, voltage={data['voltage']}")
        
        return create_response(
            success=True,
            message="Data received successfully",
            data={
                'data_id': reading['_id'],
                'device_id': reading['device_id'],
                'timestamp': reading['received_at'].isoformat()
            }
        ), 200
        
    except ValidationError as e:
        logger.warning(f"Validation error: {e.messages}")
        return create_response(
            success=False,
            message="Validation failed",
            errors=e.messages
        ), 400
        
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return create_response(
            success=False,
            message="Internal server error"
        ), 500

5.2 设备管理API
---------------
@api_bp.route('/devices', methods=['GET'])
def get_devices():
    """获取所有设备列表"""
    try:
        devices = device_model.find_all()
        
        # 统计信息
        total_devices = len(devices)
        online_devices = sum(1 for d in devices if d.get('status') == 'online')
        
        return create_response(
            success=True,
            data={
                'devices': devices,
                'statistics': {
                    'total': total_devices,
                    'online': online_devices,
                    'offline': total_devices - online_devices
                }
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting devices: {str(e)}")
        return create_response(
            success=False,
            message="Failed to retrieve devices"
        ), 500

@api_bp.route('/devices/<device_id>', methods=['GET'])
def get_device(device_id):
    """获取特定设备信息"""
    try:
        device = device_model.find_by_id(device_id)
        if not device:
            return create_response(
                success=False,
                message="Device not found"
            ), 404
        
        # 获取最新读数
        latest_reading = glucose_model.find_latest(device_id)
        
        return create_response(
            success=True,
            data={
                'device': device,
                'latest_reading': latest_reading,
                'status': device.get('status', 'unknown')
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting device {device_id}: {str(e)}")
        return create_response(
            success=False,
            message="Failed to retrieve device"
        ), 500

5.3 数据查询API
---------------
@api_bp.route('/devices/<device_id>/latest', methods=['GET'])
def get_latest_reading(device_id):
    """获取设备最新读数"""
    try:
        reading = glucose_model.find_latest(device_id)
        
        if not reading:
            return create_response(
                success=False,
                message="No readings found for this device"
            ), 404
        
        return create_response(
            success=True,
            data={
                'reading': reading,
                'timestamp': reading['timestamp'].isoformat()
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting latest reading for {device_id}: {str(e)}")
        return create_response(
            success=False,
            message="Failed to retrieve latest reading"
        ), 500

@api_bp.route('/devices/<device_id>/readings', methods=['GET'])
def get_device_readings(device_id):
    """获取设备历史读数"""
    try:
        schema = QuerySchema()
        args = schema.load(request.args)
        
        # 查询数据
        readings = glucose_model.find_by_device(
            device_id=device_id,
            limit=args['limit'],
            skip=args['offset'],
            start_date=args.get('start'),
            end_date=args.get('end')
        )
        
        # 统计总数
        total = glucose_model.count_by_device(
            device_id=device_id,
            start_date=args.get('start'),
            end_date=args.get('end')
        )
        
        # 根据格式返回数据
        if args['format'] == 'csv':
            return export_to_csv(readings, device_id)
        
        return create_response(
            success=True,
            data={
                'readings': readings,
                'pagination': {
                    'total': total,
                    'limit': args['limit'],
                    'offset': args['offset'],
                    'has_more': total > (args['offset'] + args['limit'])
                }
            }
        )
        
    except ValidationError as e:
        return create_response(
            success=False,
            message="Invalid query parameters",
            errors=e.messages
        ), 400
    except Exception as e:
        logger.error(f"Error getting readings for {device_id}: {str(e)}")
        return create_response(
            success=False,
            message="Failed to retrieve readings"
        ), 500

5.4 健康检查
------------
@api_bp.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    try:
        # 检查数据库连接
        device_model.collection.find_one()
        
        return {
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'version': '1.0.0',
            'database': 'connected'
        }
    except Exception as e:
        return {
            'status': 'unhealthy',
            'timestamp': datetime.utcnow().isoformat(),
            'error': str(e)
        }, 500

================================================================================
                          6. 工具函数 (utils.py)
================================================================================

from flask import jsonify, make_response
import csv
import io
import logging
import os
from datetime import datetime

def create_response(success=True, message="", data=None, errors=None):
    """创建标准化API响应"""
    response = {
        'success': success,
        'timestamp': datetime.utcnow().isoformat(),
        'message': message
    }

    if data is not None:
        response['data'] = data

    if errors is not None:
        response['errors'] = errors

    return jsonify(response)

def export_to_csv(readings, device_id):
    """导出数据为CSV格式"""
    output = io.StringIO()
    writer = csv.writer(output)

    # 写入标题行
    writer.writerow(['timestamp', 'current', 'voltage', 'device_id', 'received_at'])

    # 写入数据行
    for reading in readings:
        writer.writerow([
            reading['timestamp'].isoformat() if isinstance(reading['timestamp'], datetime) else reading['timestamp'],
            reading['current'],
            reading['voltage'],
            reading['device_id'],
            reading['received_at'].isoformat() if isinstance(reading['received_at'], datetime) else reading['received_at']
        ])

    # 创建响应
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'text/csv'
    response.headers['Content-Disposition'] = f'attachment; filename={device_id}_data_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'

    return response

def setup_logger(name):
    """设置日志记录器"""
    logger = logging.getLogger(name)

    if not logger.handlers:
        # 创建日志目录
        log_dir = 'logs'
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        # 设置日志级别
        logger.setLevel(logging.INFO)

        # 创建文件处理器
        file_handler = logging.FileHandler(os.path.join(log_dir, 'app.log'))
        file_handler.setLevel(logging.INFO)

        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)

        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # 添加处理器
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

    return logger

================================================================================
                          7. 配置管理 (config.py)
================================================================================

import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """应用配置类"""

    # 基础配置
    SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-change-in-production')
    DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'

    # 数据库配置
    MONGODB_URI = os.getenv('MONGODB_URI', 'mongodb://localhost:27017/nanostat')

    # API配置
    API_RATE_LIMIT = os.getenv('API_RATE_LIMIT', '1000 per minute')

    # 日志配置
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'logs/app.log')

    # CORS配置
    CORS_ORIGINS = os.getenv('CORS_ORIGINS', '*').split(',')

    @staticmethod
    def validate():
        """验证配置"""
        required_vars = ['MONGODB_URI']
        missing_vars = [var for var in required_vars if not os.getenv(var)]

        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")

================================================================================
                          8. 主应用文件 (app.py)
================================================================================

from flask import Flask
from flask_cors import CORS
from config import Config
from models import Database
from routes import api_bp, init_routes
from utils import setup_logger
import logging

def create_app():
    """应用工厂函数"""
    app = Flask(__name__)
    app.config.from_object(Config)

    # 验证配置
    Config.validate()

    # 启用CORS
    CORS(app, origins=app.config['CORS_ORIGINS'])

    # 设置日志
    setup_logger('nanostat_api')
    logger = logging.getLogger('nanostat_api')

    # 初始化数据库
    try:
        db = Database()
        logger.info("Database connection established")
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        raise

    # 初始化路由
    init_routes(db)

    # 注册蓝图
    app.register_blueprint(api_bp)

    # 根路径重定向
    @app.route('/')
    def index():
        return {
            'message': 'NanoStat API Server',
            'version': '1.0.0',
            'endpoints': {
                'health': '/health',
                'glucose_data': '/api/glucose',
                'devices': '/api/devices',
                'device_info': '/api/devices/<device_id>',
                'latest_reading': '/api/devices/<device_id>/latest',
                'device_readings': '/api/devices/<device_id>/readings',
                'device_analytics': '/api/devices/<device_id>/analytics'
            }
        }

    logger.info("NanoStat API server initialized successfully")
    return app

if __name__ == '__main__':
    app = create_app()
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=app.config['DEBUG']
    )

================================================================================
                          9. 环境配置
================================================================================

9.1 依赖包文件 (requirements.txt)
---------------------------------
Flask==2.3.3
Flask-RESTful==0.3.10
Flask-CORS==4.0.0
pymongo==4.5.0
marshmallow==3.20.1
python-dotenv==1.0.0

9.2 环境变量文件 (.env)
-----------------------
SECRET_KEY=your-super-secret-key-change-this-in-production
DEBUG=True
MONGODB_URI=mongodb://localhost:27017/nanostat
LOG_LEVEL=INFO
CORS_ORIGINS=http://localhost:3000,http://nanostat.local

================================================================================
                          10. 测试实现 (tests/test_api.py)
================================================================================

import unittest
import json
from app import create_app
from datetime import datetime

class NanoStatAPITestCase(unittest.TestCase):
    def setUp(self):
        self.app = create_app()
        self.client = self.app.test_client()
        self.app.config['TESTING'] = True

    def test_health_check(self):
        """测试健康检查端点"""
        response = self.client.get('/health')
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'healthy')

    def test_glucose_data_submission(self):
        """测试葡萄糖数据提交"""
        test_data = {
            'id': 'NANOSTAT_001',
            'timestate': datetime.utcnow().isoformat() + 'Z',
            'current': 125.67,
            'voltage': 850.2,
            'metadata': {
                'deviceType': 'NANOSTAT',
                'version': '1.0'
            }
        }

        response = self.client.post(
            '/api/glucose',
            data=json.dumps(test_data),
            content_type='application/json'
        )

        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])

    def test_invalid_device_id(self):
        """测试无效设备ID"""
        test_data = {
            'id': 'INVALID_ID',
            'timestate': datetime.utcnow().isoformat() + 'Z',
            'current': 125.67,
            'voltage': 850.2
        }

        response = self.client.post(
            '/api/glucose',
            data=json.dumps(test_data),
            content_type='application/json'
        )

        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertFalse(data['success'])

if __name__ == '__main__':
    unittest.main()

================================================================================
                          11. 部署说明
================================================================================

11.1 本地开发环境
-----------------
# 1. 克隆项目
git clone <repository-url>
cd nanostat-flask-api

# 2. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 3. 安装依赖
pip install -r requirements.txt

# 4. 设置环境变量
cp .env.example .env
# 编辑 .env 文件设置正确的MongoDB连接

# 5. 启动MongoDB
mongod --dbpath /path/to/your/db

# 6. 启动服务器
python app.py

# 7. 运行测试
python -m pytest tests/

11.2 生产环境部署
-----------------
# 使用Gunicorn部署
pip install gunicorn

# 启动命令
gunicorn --bind 0.0.0.0:5000 --workers 4 app:app

# 使用systemd服务
sudo nano /etc/systemd/system/nanostat-api.service

[Unit]
Description=NanoStat API Server
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/path/to/nanostat-flask-api
Environment="PATH=/path/to/nanostat-flask-api/venv/bin"
ExecStart=/path/to/nanostat-flask-api/venv/bin/gunicorn --bind 0.0.0.0:5000 --workers 4 app:app
Restart=always

[Install]
WantedBy=multi-user.target

# 启用服务
sudo systemctl enable nanostat-api
sudo systemctl start nanostat-api

11.3 Docker部署
---------------
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 5000

CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "4", "app:app"]

# 构建和运行
docker build -t nanostat-api .
docker run -p 5000:5000 -e MONGODB_URI=mongodb://host.docker.internal:27017/nanostat nanostat-api

================================================================================
                          12. API使用示例
================================================================================

12.1 发送数据到API
------------------
curl -X POST http://***********:5000/api/glucose \
  -H "Content-Type: application/json" \
  -d '{
    "id": "NANOSTAT_001",
    "timestate": "2024-01-15T10:30:45.123Z",
    "current": 125.67,
    "voltage": 850.2,
    "metadata": {
      "deviceType": "NANOSTAT",
      "version": "1.0",
      "units": {
        "current": "μA",
        "voltage": "mV"
      }
    }
  }'

12.2 获取设备列表
-----------------
curl -X GET http://***********:5000/api/devices

12.3 获取设备最新数据
--------------------
curl -X GET http://***********:5000/api/devices/NANOSTAT_001/latest

12.4 获取历史数据
-----------------
curl -X GET "http://***********:5000/api/devices/NANOSTAT_001/readings?limit=100&start=2024-01-15T00:00:00Z&end=2024-01-15T23:59:59Z"

12.5 健康检查
-------------
curl -X GET http://***********:5000/health

================================================================================
                              结束
================================================================================

文档版本: 1.0
最后更新: 2024-01-15
技术支持: NanoStat开发团队

注意事项:
1. 确保MongoDB服务正在运行
2. 设置正确的环境变量
3. 生产环境请更改SECRET_KEY
4. 建议使用HTTPS进行生产部署
5. 定期备份数据库
6. 监控日志文件以排查问题
