"""
数据格式化工具
Data Formatting Utilities
"""

import csv
import io
from datetime import datetime
from typing import Any, Dict, List, Optional
from bson import ObjectId


def format_timestamp(dt: datetime, include_microseconds: bool = True) -> str:
    """
    格式化时间戳为ISO 8601格式
    
    Args:
        dt: 日期时间对象
        include_microseconds: 是否包含微秒
        
    Returns:
        str: ISO格式时间戳
    """
    if include_microseconds:
        return dt.isoformat() + 'Z'
    else:
        return dt.replace(microsecond=0).isoformat() + 'Z'


def format_object_id(obj_id: ObjectId) -> str:
    """
    格式化ObjectId为字符串
    
    Args:
        obj_id: MongoDB ObjectId
        
    Returns:
        str: 字符串格式的ID
    """
    return str(obj_id) if obj_id else None


def format_float_precision(value: float, precision: int = 2) -> float:
    """
    格式化浮点数精度
    
    Args:
        value: 浮点数值
        precision: 小数位数
        
    Returns:
        float: 格式化后的值
    """
    return round(value, precision)


def format_nanostat_response(reading, include_metadata: bool = True) -> Dict[str, Any]:
    """
    格式化NANOSTAT读数响应
    
    Args:
        reading: NANOSTAT读数对象
        include_metadata: 是否包含元数据
        
    Returns:
        Dict: 格式化的响应数据
    """
    response = {
        '_id': format_object_id(reading._id),
        'device_id': reading.device_id,
        'timestamp': format_timestamp(reading.timestate),
        'timestate': format_timestamp(reading.timestate),
        'current': format_float_precision(reading.current),
        'voltage': format_float_precision(reading.voltage),
        'received_at': format_timestamp(reading.received_at)
    }
    
    if include_metadata and reading.metadata:
        response['metadata'] = reading.metadata
    
    if hasattr(reading, 'processed'):
        response['processed'] = reading.processed
    
    return response


def format_device_response(device) -> Dict[str, Any]:
    """
    格式化设备响应
    
    Args:
        device: 设备对象
        
    Returns:
        Dict: 格式化的设备数据
    """
    return {
        '_id': format_object_id(device._id),
        'device_id': device.device_id,
        'name': device.name,
        'location': device.location,
        'status': device.status,
        'last_seen': format_timestamp(device.last_seen) if device.last_seen else None,
        'created_at': format_timestamp(device.created_at),
        'updated_at': format_timestamp(device.updated_at)
    }


def format_pagination_info(total: int, limit: int, offset: int) -> Dict[str, Any]:
    """
    格式化分页信息
    
    Args:
        total: 总数
        limit: 限制数量
        offset: 偏移量
        
    Returns:
        Dict: 分页信息
    """
    return {
        'total': total,
        'limit': limit,
        'offset': offset,
        'has_more': offset + limit < total
    }


def format_statistics_response(stats: Dict[str, Any]) -> Dict[str, Any]:
    """
    格式化统计响应
    
    Args:
        stats: 统计数据
        
    Returns:
        Dict: 格式化的统计数据
    """
    formatted = {}
    
    for key, value in stats.items():
        if isinstance(value, float):
            formatted[key] = format_float_precision(value)
        elif isinstance(value, dict):
            formatted[key] = format_statistics_response(value)
        elif isinstance(value, list):
            formatted[key] = [
                format_statistics_response(item) if isinstance(item, dict) else item
                for item in value
            ]
        else:
            formatted[key] = value
    
    return formatted


def format_error_response(message: str, details: Any = None, 
                         error_code: Optional[str] = None) -> Dict[str, Any]:
    """
    格式化错误响应
    
    Args:
        message: 错误消息
        details: 错误详情
        error_code: 错误代码
        
    Returns:
        Dict: 格式化的错误响应
    """
    response = {
        'success': False,
        'message': message,
        'timestamp': format_timestamp(datetime.utcnow())
    }
    
    if error_code:
        response['error_code'] = error_code
    
    if details:
        response['errors'] = details
    
    return response


def format_success_response(data: Any = None, message: str = "Success") -> Dict[str, Any]:
    """
    格式化成功响应
    
    Args:
        data: 响应数据
        message: 成功消息
        
    Returns:
        Dict: 格式化的成功响应
    """
    response = {
        'success': True,
        'message': message,
        'timestamp': format_timestamp(datetime.utcnow())
    }
    
    if data is not None:
        response['data'] = data
    
    return response


def format_csv_data(readings: List, device_id: str) -> str:
    """
    格式化数据为CSV格式
    
    Args:
        readings: 读数列表
        device_id: 设备ID
        
    Returns:
        str: CSV格式的数据
    """
    output = io.StringIO()
    writer = csv.writer(output)
    
    # 写入表头
    writer.writerow(['timestamp', 'current', 'voltage', 'device_id'])
    
    # 写入数据
    for reading in readings:
        writer.writerow([
            format_timestamp(reading.timestate),
            format_float_precision(reading.current),
            format_float_precision(reading.voltage),
            reading.device_id
        ])
    
    return output.getvalue()


def format_uptime(start_time: datetime) -> str:
    """
    格式化运行时间
    
    Args:
        start_time: 开始时间
        
    Returns:
        str: 格式化的运行时间
    """
    uptime = datetime.utcnow() - start_time
    days = uptime.days
    hours, remainder = divmod(uptime.seconds, 3600)
    minutes, _ = divmod(remainder, 60)
    
    parts = []
    if days > 0:
        parts.append(f"{days} day{'s' if days != 1 else ''}")
    if hours > 0:
        parts.append(f"{hours} hour{'s' if hours != 1 else ''}")
    if minutes > 0:
        parts.append(f"{minutes} minute{'s' if minutes != 1 else ''}")
    
    return ', '.join(parts) if parts else 'less than 1 minute'


def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小
    
    Args:
        size_bytes: 字节数
        
    Returns:
        str: 格式化的文件大小
    """
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes / (1024 * 1024):.1f} MB"
    else:
        return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"


def format_device_status_summary(devices: List) -> Dict[str, int]:
    """
    格式化设备状态汇总
    
    Args:
        devices: 设备列表
        
    Returns:
        Dict: 状态汇总
    """
    total = len(devices)
    online = sum(1 for device in devices if device.status == 'online')
    offline = total - online
    
    return {
        'total': total,
        'online': online,
        'offline': offline
    }


def format_daily_summary(readings: List[Dict], days: int) -> List[Dict]:
    """
    格式化每日汇总数据
    
    Args:
        readings: 读数列表
        days: 天数
        
    Returns:
        List[Dict]: 每日汇总
    """
    import statistics
    
    daily_data = {}
    
    for reading in readings:
        date_str = reading['timestate'].strftime('%Y-%m-%d')
        if date_str not in daily_data:
            daily_data[date_str] = {
                'currents': [],
                'voltages': []
            }
        daily_data[date_str]['currents'].append(reading['current'])
        daily_data[date_str]['voltages'].append(reading['voltage'])
    
    summary = []
    for date_str, data in sorted(daily_data.items(), reverse=True):
        summary.append({
            'date': date_str,
            'readings_count': len(data['currents']),
            'avg_current': format_float_precision(statistics.mean(data['currents'])),
            'avg_voltage': format_float_precision(statistics.mean(data['voltages']))
        })
    
    return summary
