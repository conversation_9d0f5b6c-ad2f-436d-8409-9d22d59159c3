"""
数据验证工具
Data Validation Utilities
"""

import re
from datetime import datetime
from typing import Any, Dict, List, Optional


def validate_nanostat_device_id(device_id: str) -> bool:
    """
    验证NANOSTAT设备ID格式
    
    Args:
        device_id: 设备ID
        
    Returns:
        bool: 是否有效
    """
    pattern = r'^NANOSTAT_\d{3}$'
    return bool(re.match(pattern, device_id))


def validate_current_value(current: float) -> bool:
    """
    验证电流值范围
    
    Args:
        current: 电流值 (μA)
        
    Returns:
        bool: 是否有效
    """
    return -1000 <= current <= 1000


def validate_voltage_value(voltage: float) -> bool:
    """
    验证电压值范围
    
    Args:
        voltage: 电压值 (mV)
        
    Returns:
        bool: 是否有效
    """
    return -2000 <= voltage <= 2000


def validate_iso_datetime(datetime_str: str) -> bool:
    """
    验证ISO 8601日期时间格式
    
    Args:
        datetime_str: 日期时间字符串
        
    Returns:
        bool: 是否有效
    """
    try:
        # 支持多种ISO格式
        formats = [
            '%Y-%m-%dT%H:%M:%S.%fZ',
            '%Y-%m-%dT%H:%M:%SZ',
            '%Y-%m-%dT%H:%M:%S.%f',
            '%Y-%m-%dT%H:%M:%S'
        ]
        
        for fmt in formats:
            try:
                datetime.strptime(datetime_str, fmt)
                return True
            except ValueError:
                continue
        
        # 尝试使用fromisoformat
        datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
        return True
        
    except (ValueError, TypeError):
        return False


def validate_nanostat_metadata(metadata: Dict[str, Any]) -> List[str]:
    """
    验证NANOSTAT元数据
    
    Args:
        metadata: 元数据字典
        
    Returns:
        List[str]: 错误信息列表
    """
    errors = []
    
    if not isinstance(metadata, dict):
        errors.append("Metadata must be a dictionary")
        return errors
    
    # 验证设备类型
    device_type = metadata.get('deviceType')
    if device_type and device_type != 'NANOSTAT':
        errors.append("Device type must be 'NANOSTAT'")
    
    # 验证版本
    version = metadata.get('version')
    if version and not isinstance(version, str):
        errors.append("Version must be a string")
    
    # 验证单位
    units = metadata.get('units')
    if units:
        if not isinstance(units, dict):
            errors.append("Units must be a dictionary")
        else:
            current_unit = units.get('current')
            voltage_unit = units.get('voltage')
            
            if current_unit and current_unit != 'μA':
                errors.append("Current unit must be 'μA'")
            
            if voltage_unit and voltage_unit != 'mV':
                errors.append("Voltage unit must be 'mV'")
    
    return errors


def validate_device_name(name: str) -> bool:
    """
    验证设备名称
    
    Args:
        name: 设备名称
        
    Returns:
        bool: 是否有效
    """
    return isinstance(name, str) and 1 <= len(name.strip()) <= 100


def validate_device_location(location: str) -> bool:
    """
    验证设备位置
    
    Args:
        location: 设备位置
        
    Returns:
        bool: 是否有效
    """
    return isinstance(location, str) and 1 <= len(location.strip()) <= 200


def validate_pagination_params(limit: int, offset: int) -> Dict[str, Any]:
    """
    验证分页参数
    
    Args:
        limit: 限制数量
        offset: 偏移量
        
    Returns:
        Dict: 验证结果和调整后的参数
    """
    errors = []
    
    # 验证limit
    if limit < 1:
        errors.append("Limit must be greater than 0")
        limit = 1
    elif limit > 10000:
        errors.append("Limit cannot exceed 10000")
        limit = 10000
    
    # 验证offset
    if offset < 0:
        errors.append("Offset must be non-negative")
        offset = 0
    
    return {
        'valid': len(errors) == 0,
        'errors': errors,
        'limit': limit,
        'offset': offset
    }


def validate_time_range(start: Optional[datetime], end: Optional[datetime]) -> List[str]:
    """
    验证时间范围
    
    Args:
        start: 开始时间
        end: 结束时间
        
    Returns:
        List[str]: 错误信息列表
    """
    errors = []
    
    if start and end:
        if start >= end:
            errors.append("Start time must be before end time")
        
        # 检查时间范围是否过大（超过1年）
        if (end - start).days > 365:
            errors.append("Time range cannot exceed 365 days")
    
    # 检查时间是否在未来
    now = datetime.utcnow()
    if start and start > now:
        errors.append("Start time cannot be in the future")
    
    if end and end > now:
        errors.append("End time cannot be in the future")
    
    return errors


def sanitize_string(value: str, max_length: int = 1000) -> str:
    """
    清理字符串输入
    
    Args:
        value: 输入字符串
        max_length: 最大长度
        
    Returns:
        str: 清理后的字符串
    """
    if not isinstance(value, str):
        return str(value)
    
    # 移除前后空白
    value = value.strip()
    
    # 限制长度
    if len(value) > max_length:
        value = value[:max_length]
    
    # 移除控制字符
    value = ''.join(char for char in value if ord(char) >= 32 or char in '\t\n\r')
    
    return value


def validate_analytics_days(days: int) -> int:
    """
    验证分析天数参数
    
    Args:
        days: 天数
        
    Returns:
        int: 调整后的天数
    """
    if days < 1:
        return 1
    elif days > 365:
        return 365
    return days
