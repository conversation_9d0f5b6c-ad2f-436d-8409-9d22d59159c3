"""
NANOSTAT设备数据模型
NANOSTAT Device Data Model
"""

from datetime import datetime
from typing import Optional, Dict, Any
from bson import ObjectId
from marshmallow import Schema, fields, validate, post_load, ValidationError
import re


class NanostatReading:
    """NANOSTAT设备读数模型"""
    
    def __init__(self, device_id: str, timestate: datetime, current: float, 
                 voltage: float, metadata: Optional[Dict] = None,
                 _id: Optional[ObjectId] = None, received_at: Optional[datetime] = None,
                 processed: bool = False):
        """
        初始化NANOSTAT读数
        
        Args:
            device_id: 设备ID (格式: NANOSTAT_XXX)
            timestate: 设备时间戳
            current: 电流值 (μA)
            voltage: 电压值 (mV)
            metadata: 元数据
            _id: MongoDB文档ID
            received_at: 服务器接收时间
            processed: 是否已处理
        """
        self._id = _id
        self.device_id = device_id
        self.timestate = timestate
        self.current = current
        self.voltage = voltage
        self.metadata = metadata or {}
        self.received_at = received_at or datetime.utcnow()
        self.processed = processed
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            '_id': self._id,
            'device_id': self.device_id,
            'timestamp': self.timestate,  # 兼容性字段
            'timestate': self.timestate,
            'current': self.current,
            'voltage': self.voltage,
            'metadata': self.metadata,
            'received_at': self.received_at,
            'processed': self.processed
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'NanostatReading':
        """从字典创建实例"""
        return cls(
            _id=data.get('_id'),
            device_id=data['device_id'],
            timestate=data.get('timestate') or data.get('timestamp'),
            current=data['current'],
            voltage=data['voltage'],
            metadata=data.get('metadata', {}),
            received_at=data.get('received_at'),
            processed=data.get('processed', False)
        )


class NanostatDevice:
    """NANOSTAT设备模型"""
    
    def __init__(self, device_id: str, name: str, location: str,
                 status: str = 'offline', last_seen: Optional[datetime] = None,
                 _id: Optional[ObjectId] = None, created_at: Optional[datetime] = None,
                 updated_at: Optional[datetime] = None):
        """
        初始化NANOSTAT设备
        
        Args:
            device_id: 设备ID (格式: NANOSTAT_XXX)
            name: 设备名称
            location: 设备位置
            status: 设备状态 (online/offline)
            last_seen: 最后在线时间
            _id: MongoDB文档ID
            created_at: 创建时间
            updated_at: 更新时间
        """
        self._id = _id
        self.device_id = device_id
        self.name = name
        self.location = location
        self.status = status
        self.last_seen = last_seen
        self.created_at = created_at or datetime.utcnow()
        self.updated_at = updated_at or datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            '_id': self._id,
            'device_id': self.device_id,
            'name': self.name,
            'location': self.location,
            'status': self.status,
            'last_seen': self.last_seen,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'NanostatDevice':
        """从字典创建实例"""
        return cls(
            _id=data.get('_id'),
            device_id=data['device_id'],
            name=data['name'],
            location=data['location'],
            status=data.get('status', 'offline'),
            last_seen=data.get('last_seen'),
            created_at=data.get('created_at'),
            updated_at=data.get('updated_at')
        )


class NanostatDataSchema(Schema):
    """NANOSTAT数据接收验证模式"""
    
    id = fields.Str(required=True, validate=validate.Regexp(
        r'^NANOSTAT_\d{3}$',
        error='Device ID must follow pattern NANOSTAT_XXX'
    ))
    timestate = fields.DateTime(required=True, format='iso')
    current = fields.Float(
        required=True,
        validate=validate.Range(min=-1000, max=1000, 
                              error='Current must be between -1000 and 1000')
    )
    voltage = fields.Float(
        required=True,
        validate=validate.Range(min=-2000, max=2000,
                              error='Voltage must be between -2000 and 2000')
    )
    metadata = fields.Dict(load_default=dict)
    
    @post_load
    def make_nanostat_reading(self, data, **kwargs):
        """创建NanostatReading实例"""
        device_id = data.pop('id')  # 将'id'字段映射为'device_id'

        # 额外验证
        self._validate_device_id(device_id)
        self._validate_measurement_values(data['current'], data['voltage'])
        self._validate_metadata(data.get('metadata', {}))

        return NanostatReading(device_id=device_id, **data)

    def _validate_device_id(self, device_id):
        """验证设备ID格式"""
        if not re.match(r'^NANOSTAT_\d{3}$', device_id):
            raise ValidationError('Device ID must follow pattern NANOSTAT_XXX')

    def _validate_measurement_values(self, current, voltage):
        """验证测量值的合理性"""
        # 检查极值
        if abs(current) > 500:  # 电流超过500μA可能异常
            raise ValidationError('Current value seems abnormally high')

        if abs(voltage) > 1500:  # 电压超过1500mV可能异常
            raise ValidationError('Voltage value seems abnormally high')

        # 检查零值
        if current == 0 and voltage == 0:
            raise ValidationError('Both current and voltage cannot be zero')

    def _validate_metadata(self, metadata):
        """验证元数据"""
        if not isinstance(metadata, dict):
            return

        # 验证设备类型
        device_type = metadata.get('deviceType')
        if device_type and device_type != 'NANOSTAT':
            raise ValidationError('Device type must be NANOSTAT')

        # 验证单位
        units = metadata.get('units', {})
        if units:
            current_unit = units.get('current')
            voltage_unit = units.get('voltage')

            if current_unit and current_unit != 'μA':
                raise ValidationError('Current unit must be μA')

            if voltage_unit and voltage_unit != 'mV':
                raise ValidationError('Voltage unit must be mV')


class NanostatDeviceRegistrationSchema(Schema):
    """NANOSTAT设备注册验证模式"""
    
    device_id = fields.Str(
        required=True,
        validate=validate.Regexp(
            r'^NANOSTAT_\d{3}$',
            error='Device ID must follow pattern NANOSTAT_XXX'
        )
    )
    name = fields.Str(required=True, validate=validate.Length(min=1, max=100))
    location = fields.Str(required=True, validate=validate.Length(min=1, max=200))
    
    @post_load
    def make_nanostat_device(self, data, **kwargs):
        """创建NanostatDevice实例"""
        return NanostatDevice(**data)


class NanostatReadingResponseSchema(Schema):
    """NANOSTAT读数响应模式"""

    id = fields.Method("get_id", dump_only=True)
    device_id = fields.Str()
    timestamp = fields.Method("get_timestamp", dump_only=True)
    timestate = fields.DateTime(format='iso')
    current = fields.Float()
    voltage = fields.Float()
    metadata = fields.Dict()
    received_at = fields.DateTime(format='iso')
    processed = fields.Bool()

    def get_id(self, obj):
        """获取字符串格式的ID"""
        if hasattr(obj, '_id') and obj._id:
            return str(obj._id)
        return None

    def get_timestamp(self, obj):
        """获取timestamp字段（与timestate相同）"""
        if hasattr(obj, 'timestate') and obj.timestate:
            return obj.timestate.isoformat() + 'Z'
        return None


class NanostatDeviceResponseSchema(Schema):
    """NANOSTAT设备响应模式"""
    
    id = fields.Method("get_id", dump_only=True)
    device_id = fields.Str()
    name = fields.Str()
    location = fields.Str()
    status = fields.Str()
    last_seen = fields.DateTime(format='iso', allow_none=True)
    created_at = fields.DateTime(format='iso')
    updated_at = fields.DateTime(format='iso')
    
    def get_id(self, obj):
        """获取字符串格式的ID"""
        if hasattr(obj, '_id') and obj._id:
            return str(obj._id)
        return None
