"""
健康检查和系统状态API
Health Check and System Status API
"""

from datetime import datetime, timedelta
from flask import Flask
from flask_restx import Namespace, Resource
import psutil
import os

from app import mongo
from app.utils.responses import success_response, error_response

# 创建命名空间
health_ns = Namespace('health', description='系统健康检查')
stats_ns = Namespace('stats', description='系统统计')

# 应用启动时间
app_start_time = datetime.utcnow()


@health_ns.route('')
class HealthCheckResource(Resource):
    """健康检查资源"""
    
    @health_ns.doc('health_check')
    def get(self):
        """
        健康检查
        检查系统和数据库连接状态
        """
        try:
            # 检查数据库连接
            try:
                mongo.db.command('ping')
                database_status = 'connected'
            except Exception:
                database_status = 'disconnected'
            
            # 计算运行时间
            uptime = datetime.utcnow() - app_start_time
            uptime_str = self._format_uptime(uptime)
            
            # 系统状态
            status = 'healthy' if database_status == 'connected' else 'unhealthy'
            
            return {
                'status': status,
                'timestamp': datetime.utcnow().isoformat() + 'Z',
                'version': '1.0.0',
                'database': database_status,
                'uptime': uptime_str
            }, 200
            
        except Exception as e:
            return {
                'status': 'error',
                'timestamp': datetime.utcnow().isoformat() + 'Z',
                'message': str(e)
            }, 500
    
    def _format_uptime(self, uptime: timedelta) -> str:
        """格式化运行时间"""
        days = uptime.days
        hours, remainder = divmod(uptime.seconds, 3600)
        minutes, _ = divmod(remainder, 60)
        
        parts = []
        if days > 0:
            parts.append(f"{days} day{'s' if days != 1 else ''}")
        if hours > 0:
            parts.append(f"{hours} hour{'s' if hours != 1 else ''}")
        if minutes > 0:
            parts.append(f"{minutes} minute{'s' if minutes != 1 else ''}")
        
        return ', '.join(parts) if parts else 'less than 1 minute'


@stats_ns.route('')
class SystemStatsResource(Resource):
    """系统统计资源"""
    
    @stats_ns.doc('system_stats')
    def get(self):
        """
        系统统计
        获取系统运行统计信息
        """
        try:
            # 设备统计
            device_stats = self._get_device_stats()
            
            # 读数统计
            reading_stats = self._get_reading_stats()
            
            # 数据库大小
            db_size = self._get_database_size()
            
            # 24小时统计
            last_24h_stats = self._get_last_24h_stats()
            
            return success_response(
                data={
                    'total_devices': device_stats['total'],
                    'online_devices': device_stats['online'],
                    'total_readings': reading_stats['total'],
                    'readings_today': reading_stats['today'],
                    'database_size': db_size,
                    'last_24h_stats': last_24h_stats
                },
                message="系统统计获取成功"
            )
            
        except Exception as e:
            return error_response(
                message="获取系统统计失败",
                details=str(e),
                status_code=500
            )
    
    def _get_device_stats(self) -> dict:
        """获取设备统计"""
        try:
            devices_collection = mongo.db.nanostat_devices
            total = devices_collection.count_documents({})
            online = devices_collection.count_documents({'status': 'online'})
            
            return {
                'total': total,
                'online': online,
                'offline': total - online
            }
        except Exception:
            return {'total': 0, 'online': 0, 'offline': 0}
    
    def _get_reading_stats(self) -> dict:
        """获取读数统计"""
        try:
            readings_collection = mongo.db.nanostat_readings
            
            # 总读数
            total = readings_collection.count_documents({})
            
            # 今日读数
            today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
            today = readings_collection.count_documents({
                'received_at': {'$gte': today_start}
            })
            
            return {
                'total': total,
                'today': today
            }
        except Exception:
            return {'total': 0, 'today': 0}
    
    def _get_database_size(self) -> str:
        """获取数据库大小"""
        try:
            stats = mongo.db.command('dbstats')
            size_bytes = stats.get('dataSize', 0)
            
            # 转换为可读格式
            if size_bytes < 1024:
                return f"{size_bytes} B"
            elif size_bytes < 1024 * 1024:
                return f"{size_bytes / 1024:.1f} KB"
            elif size_bytes < 1024 * 1024 * 1024:
                return f"{size_bytes / (1024 * 1024):.1f} MB"
            else:
                return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
                
        except Exception:
            return "Unknown"
    
    def _get_last_24h_stats(self) -> dict:
        """获取最近24小时统计"""
        try:
            readings_collection = mongo.db.nanostat_readings
            
            # 24小时前的时间
            last_24h = datetime.utcnow() - timedelta(hours=24)
            
            # 24小时内的读数
            readings_24h = readings_collection.count_documents({
                'received_at': {'$gte': last_24h}
            })
            
            # 平均每分钟请求数
            avg_requests_per_minute = round(readings_24h / (24 * 60), 1)
            
            # 这里可以添加错误统计，暂时设为0
            errors = 0
            
            return {
                'readings_received': readings_24h,
                'avg_requests_per_minute': avg_requests_per_minute,
                'errors': errors
            }
            
        except Exception:
            return {
                'readings_received': 0,
                'avg_requests_per_minute': 0.0,
                'errors': 0
            }


def register_health_routes(app: Flask):
    """注册健康检查路由到Flask应用"""
    
    @app.route('/health')
    def health_check():
        """健康检查接口"""
        try:
            # 检查数据库连接
            try:
                mongo.db.command('ping')
                database_status = 'connected'
            except Exception:
                database_status = 'disconnected'
            
            # 计算运行时间
            uptime = datetime.utcnow() - app_start_time
            uptime_str = _format_uptime(uptime)
            
            # 系统状态
            status = 'healthy' if database_status == 'connected' else 'unhealthy'
            
            return {
                'status': status,
                'timestamp': datetime.utcnow().isoformat() + 'Z',
                'version': '1.0.0',
                'database': database_status,
                'uptime': uptime_str
            }, 200
            
        except Exception as e:
            return {
                'status': 'error',
                'timestamp': datetime.utcnow().isoformat() + 'Z',
                'message': str(e)
            }, 500


def _format_uptime(uptime: timedelta) -> str:
    """格式化运行时间"""
    days = uptime.days
    hours, remainder = divmod(uptime.seconds, 3600)
    minutes, _ = divmod(remainder, 60)
    
    parts = []
    if days > 0:
        parts.append(f"{days} day{'s' if days != 1 else ''}")
    if hours > 0:
        parts.append(f"{hours} hour{'s' if hours != 1 else ''}")
    if minutes > 0:
        parts.append(f"{minutes} minute{'s' if minutes != 1 else ''}")
    
    return ', '.join(parts) if parts else 'less than 1 minute'
