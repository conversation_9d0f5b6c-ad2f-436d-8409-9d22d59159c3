"""
NANOSTAT设备专用API
NANOSTAT Device Specific API
"""

from datetime import datetime
from flask import request, make_response
from flask_restx import Namespace, Resource, fields
from marshmallow import ValidationError
import csv
import io

from app.models.nanostat import (
    NanostatDataSchema, NanostatDeviceRegistrationSchema,
    NanostatReadingResponseSchema, NanostatDeviceResponseSchema
)
from app.services.nanostat_service import NanostatService
from app.utils.decorators import validate_json
from app.utils.responses import success_response, error_response

# 创建命名空间
nanostat_ns = Namespace('nanostat', description='NANOSTAT设备管理')

# 创建服务实例
nanostat_service = NanostatService()

# 创建schema实例
nanostat_data_schema = NanostatDataSchema()
device_registration_schema = NanostatDeviceRegistrationSchema()
reading_response_schema = NanostatReadingResponseSchema()
device_response_schema = NanostatDeviceResponseSchema()

# 定义API模型用于Swagger文档
nanostat_data_model = nanostat_ns.model('NanostatData', {
    'id': fields.String(required=True, description='设备ID (格式: NANOSTAT_XXX)'),
    'timestate': fields.String(required=True, description='设备时间戳 (ISO8601格式)'),
    'current': fields.Float(required=True, description='电流值 (μA)'),
    'voltage': fields.Float(required=True, description='电压值 (mV)'),
    'metadata': fields.Raw(description='元数据')
})

device_registration_model = nanostat_ns.model('DeviceRegistration', {
    'device_id': fields.String(required=True, description='设备ID (格式: NANOSTAT_XXX)'),
    'name': fields.String(required=True, description='设备名称'),
    'location': fields.String(required=True, description='设备位置')
})



@nanostat_ns.route('/devices')
class NanostatDevicesResource(Resource):
    """NANOSTAT设备列表资源"""
    
    @nanostat_ns.doc('get_all_nanostat_devices')
    def get(self):
        """
        获取所有NANOSTAT设备
        返回所有注册的NANOSTAT设备列表和统计信息
        """
        try:
            result = nanostat_service.get_all_devices()
            
            # 序列化设备数据
            devices_data = device_response_schema.dump(result['devices'], many=True)
            
            return success_response(
                data={
                    'devices': devices_data,
                    'statistics': result['statistics']
                },
                message="设备列表获取成功"
            )
            
        except Exception as e:
            return error_response(
                message="获取设备列表失败",
                details=str(e),
                status_code=500
            )
    
    @nanostat_ns.doc('register_nanostat_device')
    @nanostat_ns.expect(device_registration_model)
    @validate_json
    def post(self):
        """
        注册NANOSTAT设备
        注册新的NANOSTAT设备到系统
        """
        try:
            # 验证输入数据
            device = device_registration_schema.load(request.json)
            
            # 注册设备
            registered_device = nanostat_service.register_device(device)
            
            # 序列化响应数据
            device_data = device_response_schema.dump(registered_device)
            
            return success_response(
                data=device_data,
                message="Device registered successfully",
                status_code=201
            )
            
        except ValidationError as e:
            return error_response(
                message="Validation failed",
                details={'errors': e.messages},
                status_code=400
            )
        except Exception as e:
            if "已存在" in str(e):
                return error_response(
                    message="Device already exists",
                    details=str(e),
                    status_code=409
                )
            return error_response(
                message="Failed to register device",
                details=str(e),
                status_code=500
            )


@nanostat_ns.route('/devices/<string:device_id>')
class NanostatDeviceResource(Resource):
    """单个NANOSTAT设备资源"""
    
    @nanostat_ns.doc('get_nanostat_device')
    def get(self, device_id):
        """
        获取特定NANOSTAT设备信息
        返回指定设备的详细信息和最新读数
        """
        try:
            # 获取设备信息
            device = nanostat_service.get_device_by_id(device_id)
            if not device:
                return error_response(
                    message="Device not found",
                    status_code=404
                )
            
            # 获取最新读数
            latest_reading = nanostat_service.get_latest_reading(device_id)
            
            # 序列化数据
            device_data = device_response_schema.dump(device)
            latest_reading_data = None
            if latest_reading:
                latest_reading_data = reading_response_schema.dump(latest_reading)
            
            return success_response(
                data={
                    'device': device_data,
                    'latest_reading': latest_reading_data,
                    'status': device.status
                },
                message="设备信息获取成功"
            )
            
        except Exception as e:
            return error_response(
                message="获取设备信息失败",
                details=str(e),
                status_code=500
            )


@nanostat_ns.route('/devices/<string:device_id>/latest')
class NanostatLatestReadingResource(Resource):
    """设备最新读数资源"""
    
    @nanostat_ns.doc('get_latest_reading')
    def get(self, device_id):
        """
        获取设备最新数据
        返回指定设备的最新测量数据
        """
        try:
            # 获取最新读数
            latest_reading = nanostat_service.get_latest_reading(device_id)
            if not latest_reading:
                return error_response(
                    message="No readings found for this device",
                    status_code=404
                )
            
            # 序列化数据
            reading_data = reading_response_schema.dump(latest_reading)
            
            return success_response(
                data={
                    'reading': reading_data,
                    'timestamp': latest_reading.timestate.isoformat() + 'Z'
                },
                message="最新读数获取成功"
            )
            
        except Exception as e:
            return error_response(
                message="获取最新读数失败",
                details=str(e),
                status_code=500
            )


@nanostat_ns.route('/devices/<string:device_id>/readings')
class NanostatReadingsResource(Resource):
    """设备历史读数资源"""
    
    @nanostat_ns.doc('get_device_readings')
    def get(self, device_id):
        """
        获取设备历史数据
        返回指定设备的历史测量数据，支持时间范围筛选和分页
        """
        try:
            # 获取查询参数
            start_str = request.args.get('start')
            end_str = request.args.get('end')
            limit = request.args.get('limit', 1000, type=int)
            offset = request.args.get('offset', 0, type=int)
            format_type = request.args.get('format', 'json')
            
            # 限制参数范围
            limit = min(limit, 10000)
            
            # 解析时间参数
            start_time = None
            end_time = None
            
            if start_str:
                try:
                    start_time = datetime.fromisoformat(start_str.replace('Z', '+00:00'))
                except ValueError:
                    return error_response(
                        message="Invalid start time format",
                        status_code=400
                    )
            
            if end_str:
                try:
                    end_time = datetime.fromisoformat(end_str.replace('Z', '+00:00'))
                except ValueError:
                    return error_response(
                        message="Invalid end time format",
                        status_code=400
                    )
            
            # 获取读数数据
            result = nanostat_service.get_device_readings(
                device_id=device_id,
                start=start_time,
                end=end_time,
                limit=limit,
                offset=offset
            )
            
            # 处理CSV格式导出
            if format_type.lower() == 'csv':
                return self._export_csv(result['readings'], device_id)
            
            # JSON格式响应
            readings_data = reading_response_schema.dump(result['readings'], many=True)
            
            return success_response(
                data={
                    'readings': readings_data,
                    'pagination': result['pagination']
                },
                message="历史数据获取成功"
            )
            
        except Exception as e:
            return error_response(
                message="获取历史数据失败",
                details=str(e),
                status_code=500
            )
    
    def _export_csv(self, readings, device_id):
        """导出CSV格式数据"""
        output = io.StringIO()
        writer = csv.writer(output)
        
        # 写入表头
        writer.writerow(['timestamp', 'current', 'voltage', 'device_id'])
        
        # 写入数据
        for reading in readings:
            writer.writerow([
                reading.timestate.isoformat() + 'Z',
                reading.current,
                reading.voltage,
                reading.device_id
            ])
        
        # 创建响应
        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'text/csv'
        response.headers['Content-Disposition'] = f'attachment; filename={device_id}_data.csv'
        
        return response


@nanostat_ns.route('/devices/<string:device_id>/analytics')
class NanostatAnalyticsResource(Resource):
    """设备统计分析资源"""

    @nanostat_ns.doc('get_device_analytics')
    def get(self, device_id):
        """
        获取设备统计分析
        返回指定设备的统计分析数据
        """
        try:
            # 获取查询参数
            days = request.args.get('days', 7, type=int)

            # 限制天数范围
            days = max(1, min(days, 365))

            # 获取分析数据
            analytics = nanostat_service.get_device_analytics(device_id, days)

            return success_response(
                data=analytics,
                message="统计分析获取成功"
            )

        except Exception as e:
            return error_response(
                message="获取统计分析失败",
                details=str(e),
                status_code=500
            )


def register_nanostat_routes(app):
    """注册NANOSTAT专用路由到Flask应用"""
    from flask import request, jsonify

    @app.route('/devices', methods=['GET'])
    def get_all_nanostat_devices():
        """获取所有NANOSTAT设备"""
        try:
            result = nanostat_service.get_all_devices()
            devices_data = device_response_schema.dump(result['devices'], many=True)

            return jsonify({
                'success': True,
                'timestamp': datetime.utcnow().isoformat() + 'Z',
                'data': {
                    'devices': devices_data,
                    'statistics': result['statistics']
                }
            }), 200

        except Exception as e:
            return jsonify({
                'success': False,
                'message': '获取设备列表失败',
                'timestamp': datetime.utcnow().isoformat() + 'Z',
                'details': str(e)
            }), 500

    @app.route('/devices', methods=['POST'])
    def register_nanostat_device():
        """注册NANOSTAT设备"""
        try:
            device = device_registration_schema.load(request.json)
            registered_device = nanostat_service.register_device(device)
            device_data = device_response_schema.dump(registered_device)

            return jsonify({
                'success': True,
                'message': 'Device registered successfully',
                'timestamp': datetime.utcnow().isoformat() + 'Z',
                'data': device_data
            }), 201

        except ValidationError as e:
            return jsonify({
                'success': False,
                'message': 'Validation failed',
                'timestamp': datetime.utcnow().isoformat() + 'Z',
                'errors': e.messages
            }), 400
        except Exception as e:
            if "已存在" in str(e):
                return jsonify({
                    'success': False,
                    'message': 'Device already exists',
                    'timestamp': datetime.utcnow().isoformat() + 'Z',
                    'details': str(e)
                }), 409
            return jsonify({
                'success': False,
                'message': 'Failed to register device',
                'timestamp': datetime.utcnow().isoformat() + 'Z',
                'details': str(e)
            }), 500

    @app.route('/devices/<string:device_id>', methods=['GET'])
    def get_nanostat_device(device_id):
        """获取特定NANOSTAT设备信息"""
        try:
            device = nanostat_service.get_device_by_id(device_id)
            if not device:
                return jsonify({
                    'success': False,
                    'message': 'Device not found',
                    'timestamp': datetime.utcnow().isoformat() + 'Z'
                }), 404

            latest_reading = nanostat_service.get_latest_reading(device_id)
            device_data = device_response_schema.dump(device)
            latest_reading_data = None
            if latest_reading:
                latest_reading_data = reading_response_schema.dump(latest_reading)

            return jsonify({
                'success': True,
                'timestamp': datetime.utcnow().isoformat() + 'Z',
                'data': {
                    'device': device_data,
                    'latest_reading': latest_reading_data,
                    'status': device.status
                }
            }), 200

        except Exception as e:
            return jsonify({
                'success': False,
                'message': '获取设备信息失败',
                'timestamp': datetime.utcnow().isoformat() + 'Z',
                'details': str(e)
            }), 500

    @app.route('/devices/<string:device_id>/latest', methods=['GET'])
    def get_nanostat_latest_reading(device_id):
        """获取设备最新数据"""
        try:
            latest_reading = nanostat_service.get_latest_reading(device_id)
            if not latest_reading:
                return jsonify({
                    'success': False,
                    'message': 'No readings found for this device',
                    'timestamp': datetime.utcnow().isoformat() + 'Z'
                }), 404

            reading_data = reading_response_schema.dump(latest_reading)

            return jsonify({
                'success': True,
                'timestamp': datetime.utcnow().isoformat() + 'Z',
                'data': {
                    'reading': reading_data,
                    'timestamp': latest_reading.timestate.isoformat() + 'Z'
                }
            }), 200

        except Exception as e:
            return jsonify({
                'success': False,
                'message': '获取最新读数失败',
                'timestamp': datetime.utcnow().isoformat() + 'Z',
                'details': str(e)
            }), 500

    @app.route('/devices/<string:device_id>/readings', methods=['GET'])
    def get_nanostat_device_readings(device_id):
        """获取设备历史数据"""
        try:
            # 获取查询参数
            start_str = request.args.get('start')
            end_str = request.args.get('end')
            limit = request.args.get('limit', 1000, type=int)
            offset = request.args.get('offset', 0, type=int)
            format_type = request.args.get('format', 'json')

            # 限制参数范围
            limit = min(limit, 10000)

            # 解析时间参数
            start_time = None
            end_time = None

            if start_str:
                try:
                    start_time = datetime.fromisoformat(start_str.replace('Z', '+00:00'))
                except ValueError:
                    return jsonify({
                        'success': False,
                        'message': 'Invalid start time format',
                        'timestamp': datetime.utcnow().isoformat() + 'Z'
                    }), 400

            if end_str:
                try:
                    end_time = datetime.fromisoformat(end_str.replace('Z', '+00:00'))
                except ValueError:
                    return jsonify({
                        'success': False,
                        'message': 'Invalid end time format',
                        'timestamp': datetime.utcnow().isoformat() + 'Z'
                    }), 400

            # 获取读数数据
            result = nanostat_service.get_device_readings(
                device_id=device_id,
                start=start_time,
                end=end_time,
                limit=limit,
                offset=offset
            )

            # 处理CSV格式导出
            if format_type.lower() == 'csv':
                from app.utils.formatters import format_csv_data
                csv_data = format_csv_data(result['readings'], device_id)

                from flask import Response
                response = Response(
                    csv_data,
                    mimetype='text/csv',
                    headers={'Content-Disposition': f'attachment; filename={device_id}_data.csv'}
                )
                return response

            # JSON格式响应
            readings_data = reading_response_schema.dump(result['readings'], many=True)

            return jsonify({
                'success': True,
                'timestamp': datetime.utcnow().isoformat() + 'Z',
                'data': {
                    'readings': readings_data,
                    'pagination': result['pagination']
                }
            }), 200

        except Exception as e:
            return jsonify({
                'success': False,
                'message': '获取历史数据失败',
                'timestamp': datetime.utcnow().isoformat() + 'Z',
                'details': str(e)
            }), 500

    @app.route('/devices/<string:device_id>/analytics', methods=['GET'])
    def get_nanostat_device_analytics(device_id):
        """获取设备统计分析"""
        try:
            # 获取查询参数
            days = request.args.get('days', 7, type=int)

            # 限制天数范围
            days = max(1, min(days, 365))

            # 获取分析数据
            analytics = nanostat_service.get_device_analytics(device_id, days)

            return jsonify({
                'success': True,
                'timestamp': datetime.utcnow().isoformat() + 'Z',
                'data': analytics
            }), 200

        except Exception as e:
            return jsonify({
                'success': False,
                'message': '获取统计分析失败',
                'timestamp': datetime.utcnow().isoformat() + 'Z',
                'details': str(e)
            }), 500

    @app.route('/api/glucose/batch', methods=['POST'])
    def receive_batch_nanostat_data():
        """批量接收NANOSTAT设备数据"""
        try:
            if not isinstance(request.json, list):
                return jsonify({
                    'success': False,
                    'message': 'Batch data must be an array',
                    'timestamp': datetime.utcnow().isoformat() + 'Z'
                }), 400

            if len(request.json) > 1000:  # 限制批量大小
                return jsonify({
                    'success': False,
                    'message': 'Batch size cannot exceed 1000 items',
                    'timestamp': datetime.utcnow().isoformat() + 'Z'
                }), 400

            results = []
            errors = []

            for i, data_item in enumerate(request.json):
                try:
                    # 验证单条数据
                    reading = nanostat_data_schema.load(data_item)

                    # 保存数据
                    saved_reading = nanostat_service.save_reading(reading)

                    results.append({
                        'index': i,
                        'data_id': str(saved_reading._id),
                        'device_id': saved_reading.device_id,
                        'received_at': saved_reading.received_at.isoformat() + 'Z',
                        'status': 'success'
                    })

                except ValidationError as e:
                    errors.append({
                        'index': i,
                        'errors': e.messages,
                        'status': 'validation_error'
                    })
                except Exception as e:
                    errors.append({
                        'index': i,
                        'error': str(e),
                        'status': 'processing_error'
                    })

            # 返回批量处理结果
            response_data = {
                'total_items': len(request.json),
                'successful': len(results),
                'failed': len(errors),
                'results': results
            }

            if errors:
                response_data['errors'] = errors

            status_code = 200 if len(results) > 0 else 400
            message = f"Batch processing completed: {len(results)} successful, {len(errors)} failed"

            return jsonify({
                'success': True,
                'message': message,
                'timestamp': datetime.utcnow().isoformat() + 'Z',
                'data': response_data
            }), status_code

        except Exception as e:
            return jsonify({
                'success': False,
                'message': 'Batch processing failed',
                'timestamp': datetime.utcnow().isoformat() + 'Z',
                'details': str(e)
            }), 500

    @app.route('/api/quality/report', methods=['GET'])
    def get_data_quality_report():
        """获取数据质量报告"""
        try:
            from app.services.data_quality_service import DataQualityService
            quality_service = DataQualityService()

            # 获取查询参数
            device_id = request.args.get('device_id')
            hours = request.args.get('hours', 24, type=int)

            # 限制时间范围
            hours = max(1, min(hours, 168))  # 1小时到7天

            # 生成质量报告
            report = quality_service.generate_quality_report(device_id, hours)

            return jsonify({
                'success': True,
                'timestamp': datetime.utcnow().isoformat() + 'Z',
                'data': report
            }), 200

        except Exception as e:
            return jsonify({
                'success': False,
                'message': '获取数据质量报告失败',
                'timestamp': datetime.utcnow().isoformat() + 'Z',
                'details': str(e)
            }), 500
