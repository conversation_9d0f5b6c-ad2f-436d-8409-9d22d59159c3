"""
血糖数据API接口
Glucose Data API Endpoints
"""

from flask import request
from flask_restx import Namespace, Resource, fields
from flask_jwt_extended import jwt_required, get_jwt_identity
from marshmallow import ValidationError

from app.models.glucose import (
    GlucoseRecordSchema,
    GlucoseRecordResponseSchema,
    GlucoseQuerySchema
)
from app.models.nanostat import NanostatDataSchema
from app.services.glucose_service import GlucoseService
from app.services.nanostat_service import NanostatService
from app.utils.decorators import validate_json
from app.utils.responses import success_response, error_response

# 创建命名空间
glucose_ns = Namespace('glucose', description='血糖数据管理')

# 定义API模型用于Swagger文档
glucose_input_model = glucose_ns.model('GlucoseInput', {
    'user_id': fields.String(required=True, description='用户ID'),
    'timestamp': fields.String(required=True, description='测量时间戳 (ISO8601格式)'),
    'glucose_value': fields.Float(required=True, description='血糖值'),
    'unit': fields.String(required=True, description='单位 (mmol/L 或 mg/dL)'),
    'device_id': fields.String(description='设备ID'),
    'note': fields.String(description='备注')
})

glucose_output_model = glucose_ns.model('GlucoseOutput', {
    'id': fields.String(description='记录ID'),
    'user_id': fields.String(description='用户ID'),
    'timestamp': fields.String(description='测量时间戳'),
    'glucose_value': fields.Float(description='血糖值'),
    'unit': fields.String(description='单位'),
    'device_id': fields.String(description='设备ID'),
    'note': fields.String(description='备注'),
    'created_at': fields.String(description='创建时间')
})

# 初始化服务和模式
glucose_service = GlucoseService()
nanostat_service = NanostatService()
glucose_schema = GlucoseRecordSchema()
nanostat_schema = NanostatDataSchema()
glucose_response_schema = GlucoseRecordResponseSchema()
glucose_query_schema = GlucoseQuerySchema()


@glucose_ns.route('')
class GlucoseListResource(Resource):
    """血糖记录列表资源"""
    
    @glucose_ns.doc('create_glucose_record')
    @glucose_ns.expect(glucose_input_model)
    @validate_json
    def post(self):
        """
        创建血糖记录或接收NANOSTAT设备数据
        支持传统血糖记录格式和NANOSTAT设备数据格式
        """
        try:
            # 检查是否为NANOSTAT数据格式
            if self._is_nanostat_data(request.json):
                return self._handle_nanostat_data(request.json)
            else:
                return self._handle_glucose_data(request.json)

        except ValidationError as e:
            return error_response(
                message="Validation failed" if self._is_nanostat_data(request.json) else "输入数据验证失败",
                details={'errors': e.messages} if self._is_nanostat_data(request.json) else e.messages,
                status_code=400
            )
        except Exception as e:
            return error_response(
                message="Failed to save data" if self._is_nanostat_data(request.json) else "创建血糖记录失败",
                details=str(e),
                status_code=500
            )

    def _is_nanostat_data(self, data):
        """检查是否为NANOSTAT数据格式"""
        # 检查单条数据
        if isinstance(data, dict):
            return (
                'id' in data and
                'timestate' in data and
                'current' in data and
                'voltage' in data
            )
        # 检查批量数据
        elif isinstance(data, list) and len(data) > 0:
            return all(
                isinstance(item, dict) and
                'id' in item and
                'timestate' in item and
                'current' in item and
                'voltage' in item
                for item in data
            )
        return False

    def _handle_nanostat_data(self, data):
        """处理NANOSTAT数据（支持单条和批量）"""
        if isinstance(data, list):
            # 批量数据处理
            return self._handle_batch_nanostat_data(data)
        else:
            # 单条数据处理
            return self._handle_single_nanostat_data(data)

    def _handle_single_nanostat_data(self, data):
        """处理单条NANOSTAT数据"""
        # 验证NANOSTAT数据
        reading = nanostat_schema.load(data)

        # 保存数据
        saved_reading = nanostat_service.save_reading(reading)

        # 返回NANOSTAT格式响应
        return success_response(
            data={
                'data_id': str(saved_reading._id),
                'device_id': saved_reading.device_id,
                'received_at': saved_reading.received_at.isoformat() + 'Z'
            },
            message="Data received successfully",
            status_code=200
        )

    def _handle_batch_nanostat_data(self, data_list):
        """处理批量NANOSTAT数据"""
        results = []
        errors = []

        for i, data_item in enumerate(data_list):
            try:
                # 验证单条数据
                reading = nanostat_schema.load(data_item)

                # 保存数据
                saved_reading = nanostat_service.save_reading(reading)

                results.append({
                    'index': i,
                    'data_id': str(saved_reading._id),
                    'device_id': saved_reading.device_id,
                    'received_at': saved_reading.received_at.isoformat() + 'Z',
                    'status': 'success'
                })

            except ValidationError as e:
                errors.append({
                    'index': i,
                    'errors': e.messages,
                    'status': 'validation_error'
                })
            except Exception as e:
                errors.append({
                    'index': i,
                    'error': str(e),
                    'status': 'processing_error'
                })

        # 返回批量处理结果
        response_data = {
            'total_items': len(data_list),
            'successful': len(results),
            'failed': len(errors),
            'results': results
        }

        if errors:
            response_data['errors'] = errors

        status_code = 200 if len(results) > 0 else 400
        message = f"Batch processing completed: {len(results)} successful, {len(errors)} failed"

        return success_response(
            data=response_data,
            message=message,
            status_code=status_code
        )

    def _handle_glucose_data(self, data):
        """处理传统血糖数据"""
        # 验证血糖数据
        glucose_record = glucose_schema.load(data)

        # 保存记录
        result = glucose_service.create_record(glucose_record)

        # 返回传统格式响应
        response_data = glucose_response_schema.dump(result)
        return success_response(
            data=response_data,
            message="血糖记录创建成功",
            status_code=201
        )
    
    @glucose_ns.doc('get_glucose_records')
    def get(self):
        """
        获取血糖记录列表
        支持分页和筛选
        """
        try:
            # 验证查询参数
            query_params = glucose_query_schema.load(request.args)
            
            # 查询记录
            result = glucose_service.get_records(query_params)
            
            # 返回响应
            return success_response(
                data={
                    'records': glucose_response_schema.dump(result['records'], many=True),
                    'pagination': result['pagination']
                },
                message="查询成功"
            )
            
        except ValidationError as e:
            return error_response(
                message="查询参数验证失败",
                details=e.messages,
                status_code=400
            )
        except Exception as e:
            return error_response(
                message="查询血糖记录失败",
                details=str(e),
                status_code=500
            )


@glucose_ns.route('/<string:record_id>')
class GlucoseResource(Resource):
    """单个血糖记录资源"""
    
    @glucose_ns.doc('get_glucose_record')
    def get(self, record_id):
        """获取单个血糖记录"""
        try:
            record = glucose_service.get_record_by_id(record_id)
            if not record:
                return error_response(
                    message="记录不存在",
                    status_code=404
                )
            
            response_data = glucose_response_schema.dump(record)
            return success_response(
                data=response_data,
                message="查询成功"
            )
            
        except Exception as e:
            return error_response(
                message="查询血糖记录失败",
                details=str(e),
                status_code=500
            )
    
    @glucose_ns.doc('update_glucose_record')
    @glucose_ns.expect(glucose_input_model)
    @validate_json
    def put(self, record_id):
        """更新血糖记录"""
        try:
            # 验证输入数据
            glucose_record = glucose_schema.load(request.json)
            
            # 更新记录
            result = glucose_service.update_record(record_id, glucose_record)
            if not result:
                return error_response(
                    message="记录不存在",
                    status_code=404
                )
            
            # 返回响应
            response_data = glucose_response_schema.dump(result)
            return success_response(
                data=response_data,
                message="血糖记录更新成功"
            )
            
        except ValidationError as e:
            return error_response(
                message="输入数据验证失败",
                details=e.messages,
                status_code=400
            )
        except Exception as e:
            return error_response(
                message="更新血糖记录失败",
                details=str(e),
                status_code=500
            )
    
    @glucose_ns.doc('delete_glucose_record')
    def delete(self, record_id):
        """删除血糖记录"""
        try:
            result = glucose_service.delete_record(record_id)
            if not result:
                return error_response(
                    message="记录不存在",
                    status_code=404
                )
            
            return success_response(
                message="血糖记录删除成功"
            )
            
        except Exception as e:
            return error_response(
                message="删除血糖记录失败",
                details=str(e),
                status_code=500
            )
