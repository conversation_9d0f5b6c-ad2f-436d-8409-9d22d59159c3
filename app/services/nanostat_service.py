"""
NANOSTAT设备业务逻辑服务
NANOSTAT Device Business Logic Service
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from bson import ObjectId
from pymongo.errors import PyMongoError
import statistics

from app import mongo
from app.models.nanostat import NanostatReading, NanostatDevice


class NanostatService:
    """NANOSTAT设备服务类"""

    def __init__(self):
        pass

    @property
    def readings_collection(self):
        return mongo.db.nanostat_readings

    @property
    def devices_collection(self):
        return mongo.db.nanostat_devices
    
    def save_reading(self, reading: NanostatReading) -> NanostatReading:
        """
        保存NANOSTAT读数

        Args:
            reading: NANOSTAT读数对象

        Returns:
            NanostatReading: 保存的读数
        """
        try:
            # 数据质量检查
            quality_result = self._check_data_quality(reading)

            # 转换为字典格式
            reading_dict = reading.to_dict()
            reading_dict.pop('_id', None)  # 移除_id，让MongoDB自动生成

            # 添加质量检查结果
            reading_dict['quality_score'] = quality_result['quality_score']
            reading_dict['quality_status'] = quality_result['status']
            reading_dict['quality_issues'] = quality_result['issues']

            # 插入数据库
            result = self.readings_collection.insert_one(reading_dict)
            reading._id = result.inserted_id

            # 更新设备状态
            self._update_device_status(reading.device_id)

            return reading

        except PyMongoError as e:
            raise Exception(f"数据库操作失败: {str(e)}")

    def _check_data_quality(self, reading: NanostatReading) -> Dict[str, Any]:
        """简单的数据质量检查"""
        try:
            from app.services.data_quality_service import DataQualityService
            quality_service = DataQualityService()
            return quality_service.check_data_quality(reading)
        except Exception:
            # 如果质量检查失败，返回默认结果
            return {
                'quality_score': 100,
                'status': 'good',
                'issues': []
            }
    
    def register_device(self, device: NanostatDevice) -> NanostatDevice:
        """
        注册NANOSTAT设备
        
        Args:
            device: NANOSTAT设备对象
            
        Returns:
            NanostatDevice: 注册的设备
        """
        try:
            # 检查设备是否已存在
            existing = self.devices_collection.find_one({'device_id': device.device_id})
            if existing:
                raise Exception(f"设备 {device.device_id} 已存在")
            
            # 转换为字典格式
            device_dict = device.to_dict()
            device_dict.pop('_id', None)
            
            # 插入数据库
            result = self.devices_collection.insert_one(device_dict)
            device._id = result.inserted_id
            
            return device
            
        except PyMongoError as e:
            raise Exception(f"数据库操作失败: {str(e)}")
    
    def get_all_devices(self) -> Dict[str, Any]:
        """
        获取所有NANOSTAT设备
        
        Returns:
            Dict: 包含设备列表和统计信息
        """
        try:
            devices_cursor = self.devices_collection.find()
            devices = [NanostatDevice.from_dict(device) for device in devices_cursor]
            
            # 统计信息
            total = len(devices)
            online = sum(1 for device in devices if device.status == 'online')
            offline = total - online
            
            return {
                'devices': devices,
                'statistics': {
                    'total': total,
                    'online': online,
                    'offline': offline
                }
            }
            
        except PyMongoError as e:
            raise Exception(f"数据库查询失败: {str(e)}")
    
    def get_device_by_id(self, device_id: str) -> Optional[NanostatDevice]:
        """
        根据设备ID获取设备信息
        
        Args:
            device_id: 设备ID
            
        Returns:
            Optional[NanostatDevice]: 设备对象或None
        """
        try:
            device_dict = self.devices_collection.find_one({'device_id': device_id})
            if device_dict:
                return NanostatDevice.from_dict(device_dict)
            return None
            
        except PyMongoError as e:
            raise Exception(f"数据库查询失败: {str(e)}")
    
    def get_latest_reading(self, device_id: str) -> Optional[NanostatReading]:
        """
        获取设备最新读数
        
        Args:
            device_id: 设备ID
            
        Returns:
            Optional[NanostatReading]: 最新读数或None
        """
        try:
            reading_dict = self.readings_collection.find_one(
                {'device_id': device_id},
                sort=[('timestate', -1)]
            )
            
            if reading_dict:
                return NanostatReading.from_dict(reading_dict)
            return None
            
        except PyMongoError as e:
            raise Exception(f"数据库查询失败: {str(e)}")
    
    def get_device_readings(self, device_id: str, start: Optional[datetime] = None,
                          end: Optional[datetime] = None, limit: int = 1000,
                          offset: int = 0) -> Dict[str, Any]:
        """
        获取设备历史读数
        
        Args:
            device_id: 设备ID
            start: 开始时间
            end: 结束时间
            limit: 返回数量限制
            offset: 偏移量
            
        Returns:
            Dict: 包含读数列表和分页信息
        """
        try:
            # 构建查询条件
            filter_dict = {'device_id': device_id}
            
            if start or end:
                time_filter = {}
                if start:
                    time_filter['$gte'] = start
                if end:
                    time_filter['$lte'] = end
                filter_dict['timestate'] = time_filter
            
            # 获取总数
            total = self.readings_collection.count_documents(filter_dict)
            
            # 查询数据
            cursor = self.readings_collection.find(filter_dict)
            readings_cursor = cursor.sort('timestate', -1).skip(offset).limit(limit)
            
            readings = [NanostatReading.from_dict(reading) for reading in readings_cursor]
            
            return {
                'readings': readings,
                'pagination': {
                    'total': total,
                    'limit': limit,
                    'offset': offset,
                    'has_more': offset + limit < total
                }
            }
            
        except PyMongoError as e:
            raise Exception(f"数据库查询失败: {str(e)}")
    
    def get_device_analytics(self, device_id: str, days: int = 7) -> Dict[str, Any]:
        """
        获取设备统计分析
        
        Args:
            device_id: 设备ID
            days: 分析天数
            
        Returns:
            Dict: 统计分析结果
        """
        try:
            # 计算时间范围
            end_time = datetime.utcnow()
            start_time = end_time - timedelta(days=days)
            
            # 查询数据
            filter_dict = {
                'device_id': device_id,
                'timestate': {'$gte': start_time, '$lte': end_time}
            }
            
            readings_cursor = self.readings_collection.find(filter_dict)
            readings = list(readings_cursor)
            
            if not readings:
                return {
                    'period': f'{days} days',
                    'total_readings': 0,
                    'current_stats': {},
                    'voltage_stats': {},
                    'daily_summary': []
                }
            
            # 提取数值
            currents = [r['current'] for r in readings]
            voltages = [r['voltage'] for r in readings]
            
            # 计算统计信息
            current_stats = {
                'avg': round(statistics.mean(currents), 2),
                'min': round(min(currents), 2),
                'max': round(max(currents), 2),
                'std': round(statistics.stdev(currents) if len(currents) > 1 else 0, 2)
            }
            
            voltage_stats = {
                'avg': round(statistics.mean(voltages), 2),
                'min': round(min(voltages), 2),
                'max': round(max(voltages), 2),
                'std': round(statistics.stdev(voltages) if len(voltages) > 1 else 0, 2)
            }
            
            # 按日期分组统计
            daily_summary = self._calculate_daily_summary(readings, days)
            
            return {
                'period': f'{days} days',
                'total_readings': len(readings),
                'current_stats': current_stats,
                'voltage_stats': voltage_stats,
                'daily_summary': daily_summary
            }
            
        except PyMongoError as e:
            raise Exception(f"数据库查询失败: {str(e)}")
    
    def _update_device_status(self, device_id: str):
        """更新设备状态为在线"""
        try:
            # 检查设备是否存在
            existing_device = self.devices_collection.find_one({'device_id': device_id})

            if existing_device:
                # 设备存在，只更新状态
                self.devices_collection.update_one(
                    {'device_id': device_id},
                    {
                        '$set': {
                            'status': 'online',
                            'last_seen': datetime.utcnow(),
                            'updated_at': datetime.utcnow()
                        }
                    }
                )
            else:
                # 设备不存在，创建新设备记录
                now = datetime.utcnow()
                device_doc = {
                    'device_id': device_id,
                    'name': f'Auto-registered {device_id}',
                    'location': 'Unknown',
                    'status': 'online',
                    'last_seen': now,
                    'created_at': now,
                    'updated_at': now
                }
                self.devices_collection.insert_one(device_doc)
        except PyMongoError:
            pass  # 忽略更新错误
    
    def _calculate_daily_summary(self, readings: List[Dict], days: int) -> List[Dict]:
        """计算每日汇总统计"""
        daily_data = {}
        
        for reading in readings:
            date_str = reading['timestate'].strftime('%Y-%m-%d')
            if date_str not in daily_data:
                daily_data[date_str] = {
                    'currents': [],
                    'voltages': []
                }
            daily_data[date_str]['currents'].append(reading['current'])
            daily_data[date_str]['voltages'].append(reading['voltage'])
        
        summary = []
        for date_str, data in sorted(daily_data.items(), reverse=True):
            summary.append({
                'date': date_str,
                'readings_count': len(data['currents']),
                'avg_current': round(statistics.mean(data['currents']), 2),
                'avg_voltage': round(statistics.mean(data['voltages']), 2)
            })
        
        return summary
