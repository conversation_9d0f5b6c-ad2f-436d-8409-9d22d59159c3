"""
数据质量检查服务
Data Quality Check Service
"""

from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pymongo.errors import PyMongoError
import statistics

from app import mongo
from app.models.nanostat import NanostatReading


class DataQualityService:
    """数据质量检查服务类"""
    
    def __init__(self):
        pass
    
    @property
    def readings_collection(self):
        return mongo.db.nanostat_readings
    
    @property
    def quality_reports_collection(self):
        return mongo.db.data_quality_reports
    
    def check_data_quality(self, reading: NanostatReading) -> Dict[str, Any]:
        """
        检查单条数据的质量
        
        Args:
            reading: NANOSTAT读数对象
            
        Returns:
            Dict: 质量检查结果
        """
        quality_issues = []
        quality_score = 100
        
        # 检查数值范围
        if abs(reading.current) > 300:
            quality_issues.append({
                'type': 'range_warning',
                'field': 'current',
                'message': f'Current value {reading.current} μA is unusually high',
                'severity': 'warning'
            })
            quality_score -= 10
        
        if abs(reading.voltage) > 1200:
            quality_issues.append({
                'type': 'range_warning',
                'field': 'voltage',
                'message': f'Voltage value {reading.voltage} mV is unusually high',
                'severity': 'warning'
            })
            quality_score -= 10
        
        # 检查零值
        if reading.current == 0 and reading.voltage == 0:
            quality_issues.append({
                'type': 'zero_values',
                'field': 'both',
                'message': 'Both current and voltage are zero',
                'severity': 'error'
            })
            quality_score -= 30
        
        # 检查时间戳合理性
        now = datetime.utcnow()
        time_diff = abs((reading.timestate - now).total_seconds())
        
        if time_diff > 3600:  # 超过1小时
            quality_issues.append({
                'type': 'timestamp_warning',
                'field': 'timestate',
                'message': f'Timestamp is {time_diff/3600:.1f} hours from current time',
                'severity': 'warning'
            })
            quality_score -= 5
        
        # 检查与历史数据的一致性
        consistency_check = self._check_data_consistency(reading)
        if consistency_check['issues']:
            quality_issues.extend(consistency_check['issues'])
            quality_score -= consistency_check['score_penalty']
        
        return {
            'quality_score': max(0, quality_score),
            'issues': quality_issues,
            'status': 'good' if quality_score >= 80 else 'warning' if quality_score >= 60 else 'poor'
        }
    
    def _check_data_consistency(self, reading: NanostatReading) -> Dict[str, Any]:
        """检查数据一致性"""
        issues = []
        score_penalty = 0
        
        try:
            # 获取同设备最近的10条数据
            recent_readings = list(self.readings_collection.find(
                {
                    'device_id': reading.device_id,
                    'timestate': {'$lt': reading.timestate}
                },
                sort=[('timestate', -1)],
                limit=10
            ))
            
            if len(recent_readings) >= 3:
                recent_currents = [r['current'] for r in recent_readings]
                recent_voltages = [r['voltage'] for r in recent_readings]
                
                # 计算平均值和标准差
                avg_current = statistics.mean(recent_currents)
                std_current = statistics.stdev(recent_currents) if len(recent_currents) > 1 else 0
                
                avg_voltage = statistics.mean(recent_voltages)
                std_voltage = statistics.stdev(recent_voltages) if len(recent_voltages) > 1 else 0
                
                # 检查是否偏离过大（超过3个标准差）
                if std_current > 0:
                    current_z_score = abs(reading.current - avg_current) / std_current
                    if current_z_score > 3:
                        issues.append({
                            'type': 'outlier',
                            'field': 'current',
                            'message': f'Current value deviates significantly from recent readings (z-score: {current_z_score:.2f})',
                            'severity': 'warning'
                        })
                        score_penalty += 15
                
                if std_voltage > 0:
                    voltage_z_score = abs(reading.voltage - avg_voltage) / std_voltage
                    if voltage_z_score > 3:
                        issues.append({
                            'type': 'outlier',
                            'field': 'voltage',
                            'message': f'Voltage value deviates significantly from recent readings (z-score: {voltage_z_score:.2f})',
                            'severity': 'warning'
                        })
                        score_penalty += 15
        
        except Exception:
            pass  # 忽略一致性检查错误
        
        return {
            'issues': issues,
            'score_penalty': score_penalty
        }
    
    def generate_quality_report(self, device_id: Optional[str] = None, 
                              hours: int = 24) -> Dict[str, Any]:
        """
        生成数据质量报告
        
        Args:
            device_id: 设备ID（可选，为空则检查所有设备）
            hours: 检查时间范围（小时）
            
        Returns:
            Dict: 质量报告
        """
        try:
            # 计算时间范围
            end_time = datetime.utcnow()
            start_time = end_time - timedelta(hours=hours)
            
            # 构建查询条件
            filter_dict = {
                'received_at': {'$gte': start_time, '$lte': end_time}
            }
            
            if device_id:
                filter_dict['device_id'] = device_id
            
            # 获取数据
            readings_cursor = self.readings_collection.find(filter_dict)
            readings = list(readings_cursor)
            
            if not readings:
                return {
                    'period': f'{hours} hours',
                    'device_id': device_id,
                    'total_readings': 0,
                    'quality_summary': {},
                    'issues_summary': {},
                    'recommendations': []
                }
            
            # 分析数据质量
            quality_scores = []
            all_issues = []
            
            for reading_dict in readings:
                reading = NanostatReading.from_dict(reading_dict)
                quality_result = self.check_data_quality(reading)
                quality_scores.append(quality_result['quality_score'])
                all_issues.extend(quality_result['issues'])
            
            # 统计质量分数
            avg_quality = statistics.mean(quality_scores)
            min_quality = min(quality_scores)
            max_quality = max(quality_scores)
            
            # 统计问题类型
            issues_by_type = {}
            issues_by_severity = {}
            
            for issue in all_issues:
                issue_type = issue['type']
                severity = issue['severity']
                
                issues_by_type[issue_type] = issues_by_type.get(issue_type, 0) + 1
                issues_by_severity[severity] = issues_by_severity.get(severity, 0) + 1
            
            # 生成建议
            recommendations = self._generate_recommendations(
                avg_quality, issues_by_type, issues_by_severity
            )
            
            return {
                'period': f'{hours} hours',
                'device_id': device_id,
                'total_readings': len(readings),
                'quality_summary': {
                    'average_score': round(avg_quality, 2),
                    'min_score': min_quality,
                    'max_score': max_quality,
                    'good_readings': sum(1 for score in quality_scores if score >= 80),
                    'warning_readings': sum(1 for score in quality_scores if 60 <= score < 80),
                    'poor_readings': sum(1 for score in quality_scores if score < 60)
                },
                'issues_summary': {
                    'by_type': issues_by_type,
                    'by_severity': issues_by_severity,
                    'total_issues': len(all_issues)
                },
                'recommendations': recommendations
            }
            
        except PyMongoError as e:
            raise Exception(f"数据库查询失败: {str(e)}")
    
    def _generate_recommendations(self, avg_quality: float, 
                                issues_by_type: Dict, 
                                issues_by_severity: Dict) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        if avg_quality < 70:
            recommendations.append("Overall data quality is poor. Consider reviewing sensor calibration.")
        
        if issues_by_type.get('range_warning', 0) > 10:
            recommendations.append("Multiple range warnings detected. Check sensor configuration and environmental conditions.")
        
        if issues_by_type.get('outlier', 0) > 5:
            recommendations.append("Frequent outliers detected. Consider implementing data smoothing or checking for interference.")
        
        if issues_by_type.get('zero_values', 0) > 0:
            recommendations.append("Zero values detected. Check sensor connections and power supply.")
        
        if issues_by_severity.get('error', 0) > 0:
            recommendations.append("Critical errors found. Immediate attention required.")
        
        if not recommendations:
            recommendations.append("Data quality is good. Continue current monitoring practices.")
        
        return recommendations
    
    def save_quality_report(self, report: Dict[str, Any]) -> str:
        """保存质量报告"""
        try:
            report['generated_at'] = datetime.utcnow()
            result = self.quality_reports_collection.insert_one(report)
            return str(result.inserted_id)
        except PyMongoError as e:
            raise Exception(f"保存质量报告失败: {str(e)}")
